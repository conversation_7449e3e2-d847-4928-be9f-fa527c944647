import { Link } from "react-router-dom";
import BBB_Image from "/src/assets/bbb-review.webp";
import TrustPilot_Image from "/src/assets/trustpilot-review.webp";
import { trackClick } from "../../utils/analytics.js";
import { MapPin } from "lucide-react";
import { SiFacebook, SiInstagram } from "@icons-pack/react-simple-icons";
import * as Icons from '@icons-pack/react-simple-icons';
console.log(Icons);


const Copyright = () => {
  const handlePrivacyPolicyClick = () => {
    trackClick("Privacy Policy", {
      destination: "https://pinnaclefundingco.com/privacy-policy/",
    });
  };

  const handleTermsClick = () => {
    trackClick("Terms & Conditions", {
      destination: "https://pinnaclefundingco.com/privacy-policy/",
    });
  };

  return (
    <div className="text-xs sm:text-sm px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 sm:gap-0">
        <p>
          &copy; {new Date().getFullYear()} Pinnacle Funding. All rights
          reserved.
        </p>
        <p>
          <span className="hover:underline">
            <a
              target="_blank"
              href="https://pinnaclefundingco.com/privacy-policy/"
              onClick={handlePrivacyPolicyClick}
            >
              Privacy Policy
            </a>
          </span>{" "}
          |{" "}
          <span className="hover:underline">
            <a
              target="_blank"
              href="https://pinnaclefundingco.com/privacy-policy/"
              onClick={handleTermsClick}
            >
              Terms & Conditions{" "}
            </a>
          </span>
        </p>
      </div>
    </div>
  );
};

const TrustPilotImg = () => {
  const handleTrustpilotClick = () => {
    trackClick("Trustpilot", {
      destination: "https://www.trustpilot.com/review/pinnacleconsultingny.com",
    });
  };

  return (
    <a
      target="_blank"
      href="https://www.trustpilot.com/review/pinnacleconsultingny.com"
      onClick={handleTrustpilotClick}
    >
      <img
        src={TrustPilot_Image}
        alt="Trustpilot"
        className="h-8 sm:h-10 lg:h-12 rounded-sm"
      />
    </a>
  );
};

const BBBImg = () => {
  const handleBBBClick = () => {
    trackClick("BBB", {
      destination:
        "https://www.bbb.org/us/ny/brooklyn/profile/financial-services/pinnacle-funding-0121-87175014",
    });
  };

  return (
    <a
      target="_blank"
      href="https://www.bbb.org/us/ny/brooklyn/profile/financial-services/pinnacle-funding-0121-87175014"
      onClick={handleBBBClick}
    >
      <img
        src={BBB_Image}
        alt="Better Business Bureau"
        className="h-8 sm:h-10 lg:h-12"
      />
    </a>
  );
};

const LoanOptions = () => {
  const handleLoanOptionClick = (option) => {
    trackClick("Footer Loan Option", { option });
  };

  return (
    <div>
      <h3 className="text-lg font-semibold text-white mb-4">Loan Options</h3>
      <ul className="space-y-2">
        <li>
          <a
            href="https://pinnaclefundingco.com/business-expansion/"
            target="_blank"
            rel="noopener noreferrer"
            className="hover:text-white transition-colors duration-200"
            onClick={() => handleLoanOptionClick("Business Expansion")}
          >
            Business Expansion
          </a>
        </li>
        <li>
          <a
            href="https://pinnaclefundingco.com/term-loan-2/"
            target="_blank"
            rel="noopener noreferrer"
            className="hover:text-white transition-colors duration-200"
            onClick={() => handleLoanOptionClick("Term Loan")}
          >
            Term Loan
          </a>
        </li>
        <li>
          <a
            href="https://pinnaclefundingco.com/line-of-credit/"
            target="_blank"
            rel="noopener noreferrer"
            className="hover:text-white transition-colors duration-200"
            onClick={() => handleLoanOptionClick("Line Of Credit")}
          >
            Line Of Credit
          </a>
        </li>
      </ul>
    </div>
  );
};

const Resources = () => {
  const handleResourceClick = (resource) => {
    trackClick("Footer Resource", { resource });
  };

  return (
    <div>
      <h3 className="text-lg font-semibold text-white mb-4">Resources</h3>
      <ul className="space-y-2">
        <li>
          <a
            href="https://pinnaclefundingco.com/faq/"
            target="_blank"
            rel="noopener noreferrer"
            className="hover:text-white transition-colors duration-200"
            onClick={() => handleResourceClick("FAQ")}
          >
            FAQ
          </a>
        </li>
        <li>
          <a
            href="https://pinnaclefundingco.com/resources/"
            target="_blank"
            rel="noopener noreferrer"
            className="hover:text-white transition-colors duration-200"
            onClick={() => handleResourceClick("Resources")}
          >
            Resources
          </a>
        </li>
        <li>
          <a
            href="https://pinnaclefundingco.com/about-small-business-success/"
            target="_blank"
            rel="noopener noreferrer"
            className="hover:text-white transition-colors duration-200"
            onClick={() => handleResourceClick("About")}
          >
            About
          </a>
        </li>
        <li>
          <a
            href="https://pinnaclefundingco.com/contact-us/"
            target="_blank"
            rel="noopener noreferrer"
            className="hover:text-white transition-colors duration-200"
            onClick={() => handleResourceClick("Contact Us")}
          >
            Contact Us
          </a>
        </li>
      </ul>
    </div>
  );
};

const FollowUs = () => {
  const handleSocialClick = (platform) => {
    trackClick("Footer Social", { platform });
  };

  const handleAddressClick = () => {
    trackClick("Footer Address");
  };

  return (
    <div>
      <h3 className="text-lg font-semibold text-white mb-4">Follow Us</h3>
      <div className="flex space-x-4 mb-6">
        <a
          href="https://www.facebook.com/profile.php?id=61565186224953"
          target="_blank"
          rel="noopener noreferrer"
          className="hover:text-white transition-colors duration-200"
          onClick={() => handleSocialClick("Facebook")}
        >
          <SiFacebook size={24} />
        </a>
        <a
          href="https://www.linkedin.com/company/pinnacle-fundingus"
          target="_blank"
          rel="noopener noreferrer"
          className="hover:text-white transition-colors duration-200"
          onClick={() => handleSocialClick("LinkedIn")}
        >
          {/* embedded directly since the icon was removed from the package */}
          <svg viewBox="0 0 24 24" width="24" height="24" xmlns="http://www.w3.org/2000/svg" fill="currentColor"><title>LinkedIn</title><path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" /></svg>
        </a>
        <a
          href="https://www.instagram.com/pinnacle_funding/"
          target="_blank"
          rel="noopener noreferrer"
          className="hover:text-white transition-colors duration-200"
          onClick={() => handleSocialClick("Instagram")}
        >
          <SiInstagram size={24} />
        </a>
      </div>
      <div className="flex items-start space-x-2">
        <MapPin size={20} className="mt-0.5 flex-shrink-0" />
        <a
          href="https://maps.app.goo.gl/ERTtVxHzoY2AMZHH9"
          target="_blank"
          rel="noopener noreferrer"
          className="hover:text-white transition-colors duration-200"
          onClick={handleAddressClick}
        >
          <div>
            <div>45 Rockefeller Plaza 20FL</div>
            <div>New York, NY 10111</div>
          </div>
        </a>
      </div>
    </div>
  );
};

export const Footer = () => {
  return (
    <footer className="bg-[#23448F] text-[#B4C2F8] py-4">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="lg:col-span-1">
            <p className="mb-4">
              <Link
                onClick={() => trackClick("Footer Logo")}
                className="font-bold text-xl sm:text-2xl text-white"
                to={"/"}
              >
                Pinnacle Funding
              </Link>
            </p>
            <p className="text-sm font-light sm:text-base mb-6">
              We are a premier alternative lender proudly serving small and
              mid-sized businesses.
            </p>
            <div className="flex gap-3 sm:gap-4">
              <TrustPilotImg />
              <BBBImg />
            </div>
          </div>

          {/* Loan Options */}
          <div>
            <LoanOptions />
          </div>

          {/* Resources */}
          <div>
            <Resources />
          </div>

          {/* Follow Us */}
          <div>
            <FollowUs />
          </div>
        </div>
      </div>

      <hr className="w-full h-px max-w-7xl my-3 sm:my-4 mx-auto" />
      <div className="py-3 sm:py-4">
        <Copyright />
      </div>
    </footer>
  );
};

export default Footer;
